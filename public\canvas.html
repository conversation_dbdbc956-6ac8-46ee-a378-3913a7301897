<!DOCTYPE html>
<html>
<head>
  <title>AI Generated HTML - Canvas</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { 
      margin: 0; 
      padding: 0;
      overflow: auto; 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      font-size: 18px;
      color: #666;
    }
    .error {
      display: none;
      justify-content: center;
      align-items: center;
      height: 100vh;
      padding: 20px;
      text-align: center;
      color: #d32f2f;
      background-color: #fafafa;
    }
    .error h2 {
      color: #d32f2f;
      margin-bottom: 10px;
    }
    .error p {
      margin: 5px 0;
      color: #666;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">Loading AI-generated content...</div>
  <div id="error" class="error">
    <div>
      <h2>Canvas Error</h2>
      <p>No HTML content found in URL parameters.</p>
      <p>This page is designed to display AI-generated HTML content.</p>
    </div>
  </div>

  <script>
    function loadCanvasContent() {
      try {
        console.log('Loading canvas content...');
        
        // Get the HTML content from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const encodedHtml = urlParams.get('html');

        if (!encodedHtml) {
          console.error('No HTML content found in URL parameters');
          document.getElementById('loading').style.display = 'none';
          document.getElementById('error').style.display = 'flex';
          return;
        }

        console.log('Encoded HTML found, decoding...');
        
        // Decode the HTML content
        let htmlContent;
        try {
          htmlContent = decodeURIComponent(encodedHtml);
        } catch (decodeError) {
          console.error('Failed to decode HTML content:', decodeError);
          throw new Error('Invalid HTML encoding');
        }

        console.log('HTML decoded successfully, length:', htmlContent.length);
        console.log('HTML preview:', htmlContent.substring(0, 200) + '...');

        // Validate that we have actual HTML content
        if (!htmlContent.trim()) {
          throw new Error('Decoded HTML content is empty');
        }

        // Check if this is a complete HTML document
        const isCompleteDocument = htmlContent.includes('<!DOCTYPE html>') || 
                                 htmlContent.includes('<html') || 
                                 htmlContent.includes('<head>') || 
                                 htmlContent.includes('<body>');

        if (isCompleteDocument) {
          console.log('Complete HTML document detected, replacing entire page...');
          // Replace the entire document with the decoded HTML
          document.open();
          document.write(htmlContent);
          document.close();
        } else {
          console.log('HTML fragment detected, inserting into page...');
          // Hide loading and error divs
          document.getElementById('loading').style.display = 'none';
          document.getElementById('error').style.display = 'none';
          
          // Create a container for the HTML content
          const container = document.createElement('div');
          container.innerHTML = htmlContent;
          
          // Replace body content with the HTML
          document.body.innerHTML = '';
          document.body.appendChild(container);
        }

        console.log('Canvas content loaded successfully');

      } catch (error) {
        console.error('Error loading canvas content:', error);
        
        // Hide loading indicator
        const loadingEl = document.getElementById('loading');
        if (loadingEl) {
          loadingEl.style.display = 'none';
        }
        
        // Show error message
        const errorEl = document.getElementById('error');
        if (errorEl) {
          errorEl.style.display = 'flex';
          errorEl.innerHTML = `
            <div>
              <h2>Canvas Error</h2>
              <p>Failed to load HTML content: ${error.message}</p>
              <p>Please check the console for more details.</p>
              <details style="margin-top: 10px;">
                <summary>Debug Information</summary>
                <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin-top: 5px; color: #333; font-size: 12px; overflow: auto; max-height: 200px;">URL: ${window.location.href}
Error: ${error.stack || error.message}
Timestamp: ${new Date().toISOString()}</pre>
              </details>
            </div>
          `;
        }
      }
    }

    // Load content when page is ready
    console.log('Canvas script loaded, document ready state:', document.readyState);
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadCanvasContent);
    } else {
      loadCanvasContent();
    }
  </script>
</body>
</html>
