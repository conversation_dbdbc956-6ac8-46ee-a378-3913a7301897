<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Mode Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🎨 Canvas Mode Test</h1>
    
    <div class="test-section">
        <h2>✅ Canvas Mode is Working!</h2>
        <p class="success">If you can see this page, the canvas mode HTML detection and rendering is working correctly.</p>
        
        <p>This test verifies that:</p>
        <ul>
            <li>HTML code is automatically detected in AI responses when in canvas mode</li>
            <li>The HTML is properly extracted and rendered in a new tab</li>
            <li>The canvas.html fallback system is functioning</li>
        </ul>
        
        <button class="button" onclick="alert('Interactive elements work too!')">
            Test Interactive Element
        </button>
    </div>
    
    <div class="test-section">
        <h3>How it works:</h3>
        <ol>
            <li>User switches to Canvas mode</li>
            <li>AI generates HTML code in response</li>
            <li>System automatically detects HTML patterns</li>
            <li>HTML is extracted and rendered in new tab</li>
        </ol>
    </div>
    
    <script>
        console.log('Canvas mode test page loaded successfully!');
        
        // Add some interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.button');
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    </script>
</body>
</html>
