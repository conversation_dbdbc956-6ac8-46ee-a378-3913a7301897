<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cute Satisfying <PERSON><PERSON></title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f8ff; /* Light sky blue background */
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden; /* Prevent scrollbar from appearing if button scales slightly */
        }

        .satisfying-button {
            background-color: #87CEEB; /* Sky Blue */
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px; /* Very rounded corners for a cute look */
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(.25,.8,.25,1); /* Smooth transition for all properties */
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .satisfying-button:hover {
            background-color: #6A5ACD; /* Slate Blue on hover */
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
            transform: translateY(-3px) scale(1.02); /* Lift and slightly enlarge */
            letter-spacing: 1.5px; /* Subtle text spread */
        }

        .satisfying-button:active {
            background-color: #483D8B; /* Dark Slate Blue on click */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transform: translateY(1px) scale(0.98); /* Press down effect */
            transition: all 0.1s ease-in-out; /* Quicker transition for active state */
        }

        /* Optional: Add a subtle ripple effect on click */
        .satisfying-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            opacity: 0;
            transform: scale(1);
            transition: all 0.5s cubic-bezier(.25,.8,.25,1);
        }

        .satisfying-button:active::after {
            transform: scale(200); /* Expand ripple */
            opacity: 1;
            transition: 0s; /* Instant start for ripple */
        }
    </style>
</head>
<body>

    <button class="satisfying-button">
        Click Me!
    </button>

</body>
</html>
